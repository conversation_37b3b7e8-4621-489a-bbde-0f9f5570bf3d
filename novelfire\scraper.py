import requests
import json
import re
import time
import random
import os
import cloudscraper  # Import the cloudscraper library
import brotli  # Import brotli for decompression
from bs4 import BeautifulSoup
from flask import Flask, jsonify, request
from urllib.parse import urlparse, quote_plus
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed

# Initialize the Flask application
app = Flask(__name__)

# --- Helper Functions for Scraping ---

def create_robust_scraper():
    """Creates a cloudscraper instance with optimal settings for Vercel deployment."""
    return cloudscraper.create_scraper(
        browser={
            'browser': 'chrome',
            'platform': 'windows',
            'mobile': False
        },
        delay=random.uniform(2, 5),  # Increased random delay for serverless
        captcha={
            'provider': 'return_response'  # Don't try to solve captchas automatically
        },
        # Add more realistic browser behavior
        doubleDown=True,
        disableCloudflareV1=False
    )

def get_browser_headers(referer=None):
    """Returns comprehensive browser headers to avoid detection."""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none' if not referer else 'same-origin',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }

    if referer:
        headers['Referer'] = referer
        headers['Sec-Fetch-Site'] = 'same-origin'

    return headers

def robust_request(url, max_retries=3, delay_range=(1, 3)):
    """Makes a robust HTTP request with retry logic and random delays."""
    scraper = create_robust_scraper()
    headers = get_browser_headers()

    for attempt in range(max_retries):
        try:
            # Add random delay to avoid rate limiting
            if attempt > 0:
                delay = random.uniform(*delay_range)
                time.sleep(delay)

            response = scraper.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            return response

        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:  # Last attempt
                raise e

            # Exponential backoff for retries
            wait_time = (2 ** attempt) + random.uniform(0, 1)
            time.sleep(wait_time)

    return None

def scrape_trending_novels():
    """ Scrapes the "Most Read" novels from the homepage. """
    url = 'https://novelfire.net/home'

    try:
        response = robust_request(url)
    except requests.exceptions.RequestException as e:
        # Try alternative approach with different user agent if first attempt fails
        try:
            scraper = cloudscraper.create_scraper()
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            response = scraper.get(url, headers=headers, timeout=45)
            response.raise_for_status()
        except requests.exceptions.RequestException as e2:
            return {'error': f'Could not retrieve homepage. Primary error: {e}, Fallback error: {e2}'}, 500

    soup = BeautifulSoup(response.content, 'html.parser')
    novels = []
    section = soup.find('div', class_='rank-container')
    if section:
        for item in section.find_all('li', class_='novel-item'):
            title_element = item.find('h4', class_='novel-title').find('a')
            stats = item.find('div', class_='item-body').find_all('span')
            novels.append({
                'title': title_element.get('title'),
                'url': title_element.get('href'),
                'image_url': item.find('div', class_='novel-cover').find('img').get('data-src'),
                'monthly_views': stats[0].text.strip(),
                'bookmarks': stats[1].text.strip()
            })
    return novels

def search_novels(query):
    """ Searches for novels using the website's live search AJAX endpoint. """
    encoded_query = quote_plus(query)
    url = f'https://novelfire.net/ajax/searchLive?inputContent={encoded_query}'

    # Use cloudscraper for search as well
    scraper = cloudscraper.create_scraper(
        browser={
            'browser': 'chrome',
            'platform': 'windows',
            'mobile': False
        }
    )

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'X-Requested-With': 'XMLHttpRequest',
        'Referer': 'https://novelfire.net/',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }

    try:
        response = scraper.get(url, headers=headers, timeout=30)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        return {'error': f'Could not perform search. Reason: {e}'}, 500
    
    soup = BeautifulSoup(response.json().get('html', ''), 'html.parser')
    results = []
    for item in soup.find_all('li', class_='novel-item'):
        stats = item.find_all('div', class_='novel-stats')
        results.append({
            'title': item.find('h4', class_='novel-title').text.strip(),
            'url': item.find('a').get('href'),
            'image_url': item.find('img').get('src'),
            'rank': stats[0].text.strip() if stats else 'N/A',
            'chapters': stats[1].text.strip() if len(stats) > 1 else 'N/A'
        })
    return results

def fetch_and_parse_chapter_page(url):
    """ Fetches and parses a single page of chapters for the chapter list. """
    try:
        # Use cloudscraper for chapter pages too
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'mobile': False
            }
        )

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        response = scraper.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')
        page_chapters = []
        for item in soup.select('.chapter-list li a'):
            page_chapters.append({
                'url': item.get('href'),
                'chapter_number': item.select_one('.chapter-no').text.strip(),
                'title': item.select_one('.chapter-title').text.strip(),
                'last_updated': item.select_one('.chapter-update').text.strip()
            })
        return page_chapters
    except requests.exceptions.RequestException:
        return []

def scrape_novel_details(novel_url):
    """ Scrapes metadata and ALL chapters for a novel concurrently. """
    # Use cloudscraper for novel details
    scraper = cloudscraper.create_scraper(
        browser={
            'browser': 'chrome',
            'platform': 'windows',
            'mobile': False
        }
    )

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }

    try:
        response = scraper.get(novel_url, headers=headers, timeout=30)
        if response.status_code == 404: return {'error': 'Novel not found'}, 404
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        return {'error': f'Could not retrieve novel page. Reason: {e}'}, 500

    soup = BeautifulSoup(response.content, 'html.parser')
    
    novel_data = {
        'title': soup.find('h1', class_='novel-title').text.strip(),
        'author': soup.select_one('.author a').text.strip(),
        'rank': soup.select_one('.rank strong').text.strip(),
        'rating': soup.find('strong', class_='nub').text.strip(),
        'cover_image_url': soup.select_one('.cover img').get('data-src'),
        'summary': soup.select_one('.summary .content').text.strip().replace('Show More', ''),
        'genres': [genre.text for genre in soup.select('.categories ul li a')],
        'stats': {
            'chapters': soup.select_one('.header-stats span:nth-of-type(1) strong').text.strip(),
            'views': soup.select_one('.header-stats span:nth-of-type(2) strong').text.strip(),
            'bookmarked': soup.select_one('.header-stats span:nth-of-type(3) strong').text.strip(),
            'status': soup.select_one('.header-stats span:nth-of-type(4) strong').text.strip()
        },
        'chapters': []
    }

    novel_slug = novel_url.strip('/').split('/')[-1]
    chapters_base_url = f'https://novelfire.net/book/{novel_slug}/chapters'
    
    first_page_response = scraper.get(chapters_base_url, headers=headers, timeout=30)
    first_page_soup = BeautifulSoup(first_page_response.content, 'html.parser')
    
    last_page = 1
    if pagination_links := first_page_soup.select('.pagination .page-item a.page-link'):
        try: last_page = int(pagination_links[-2].text)
        except (ValueError, IndexError): pass
    
    all_chapters = []
    urls_to_fetch = [f"{chapters_base_url}?page={i}" for i in range(1, last_page + 1)]

    with ThreadPoolExecutor(max_workers=10) as executor:
        for result in executor.map(fetch_and_parse_chapter_page, urls_to_fetch):
            all_chapters.extend(result)
    
    all_chapters.sort(key=lambda x: int(re.search(r'\d+', x['chapter_number']).group() or 0))
    novel_data['chapters'] = all_chapters
    return novel_data

def scrape_chapter_content(chapter_url):
    """ Scrapes the content and navigation details of a specific chapter. """
    scraper = cloudscraper.create_scraper()
    try:
        response = scraper.get(chapter_url, timeout=15)
        if response.status_code == 404: return {'error': 'Chapter not found'}, 404
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        return {'error': f'Could not retrieve chapter page. Reason: {e}'}, 500

    soup = BeautifulSoup(response.content, 'html.parser')
    
    content_div = soup.find('div', id='content')
    if not content_div:
        return {'error': 'Could not find chapter content on the page.'}, 500
    
    paragraphs = [p.get_text(strip=True) for p in content_div.find_all('p')]
    content = '\n\n'.join(p for p in paragraphs if p)

    script_text = soup.find('script', string=re.compile(r'post_id=parseInt'))
    post_id = re.search(r'post_id=parseInt\("(\d+)"\)', script_text.string).group(1) if script_text else None
    chapter_id = re.search(r'chapter_id=parseInt\("(\d+)"\)', script_text.string).group(1) if script_text else None

    prev_chap_tag = soup.select_one('a.prevchap')
    next_chap_tag = soup.select_one('a.nextchap')
    
    return {
        'novel_title': soup.select_one('.booktitle').text.strip(),
        'chapter_title': soup.select_one('.chapter-title').text.strip(),
        'content': content,
        'navigation': {
            'previous_url': None if 'isDisabled' in prev_chap_tag.get('class', []) else prev_chap_tag.get('href'),
            'next_url': None if 'isDisabled' in next_chap_tag.get('class', []) else next_chap_tag.get('href')
        },
        'comment_data': {
            'post_id': post_id,
            'chapter_id': chapter_id
        }
    }

def fetch_comments(post_id, chapter_id):
    """ (REVISED & FIXED) Fetches all comments using requests session with proper headers and session management. """
    page = 1
    all_comments = []

    # Create a requests session to maintain cookies and handle compression
    session = requests.Session()
    # Add brotli support to the session
    session.headers.update({'Accept-Encoding': 'gzip, deflate, br'})

    # First, visit the chapter page to establish a proper session and get cookies
    try:
        # Visit the specific chapter page to get proper session cookies
        chapter_url = f'https://novelfire.net/book/shadow-slave/chapter-2'
        headers_init = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-GB,en;q=0.9,ar-EG;q=0.8,ar;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1'
        }
        init_response = session.get(chapter_url, headers=headers_init, timeout=15)
        init_response.raise_for_status()
        print(f"DEBUG: Initial session established, status: {init_response.status_code}")
        print(f"DEBUG: Session cookies: {session.cookies}")
    except requests.exceptions.RequestException as e:
        print(f"DEBUG: Failed to establish initial session: {e}")
        return []

    # Set up headers that match the browser request exactly for AJAX calls
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br',  # Remove zstd as it's causing issues
        'Accept-Language': 'en-GB,en;q=0.9,ar-EG;q=0.8,ar;q=0.7,en-US;q=0.6',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Referer': 'https://novelfire.net/book/shadow-slave/chapter-2',
        'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest'
    }

    while True:
        url = f"https://novelfire.net/comment/show?post_id={post_id}&chapter_id={chapter_id}&order_by=newest&page={page}"
        print(f"DEBUG: Fetching comments from {url}")

        try:
            # Use the session with proper headers
            response = session.get(url, headers=headers, timeout=15)
            print(f"DEBUG: Response status: {response.status_code}")
            print(f"DEBUG: Response headers: {dict(response.headers)}")

            if response.status_code == 403:
                print("DEBUG: 403 Forbidden - trying to refresh session")
                # Try to refresh session by visiting chapter page again
                session.get(chapter_url, headers=headers_init, timeout=15)
                response = session.get(url, headers=headers, timeout=15)

            response.raise_for_status()

            # Debug the response
            print(f"DEBUG: Response content (first 200 chars): {response.text[:200]}")
            print(f"DEBUG: Content encoding: {response.headers.get('content-encoding')}")

            # Check if response is JSON
            try:
                data = response.json()
                print(f"DEBUG: Successfully parsed JSON: {data.keys() if isinstance(data, dict) else type(data)}")
            except json.JSONDecodeError as e:
                print(f"DEBUG: JSON decode error: {e}")
                print(f"DEBUG: Response content type: {response.headers.get('content-type')}")
                break

            if data.get('status') == 'error':
                print(f"DEBUG: Server returned error on page {page}: {data.get('message')}")
                break

            html_content = data.get('html', '').strip()
            if not html_content:
                print(f"DEBUG: No more HTML content on page {page}. Stopping.")
                break

            print(f"DEBUG: HTML content length: {len(html_content)}")
            soup = BeautifulSoup(html_content, 'html.parser')

            # Try different selectors to find comments
            comments_on_page = soup.select('li.comment')
            if not comments_on_page:
                comments_on_page = soup.select('li')  # Try just li elements
                print(f"DEBUG: Found {len(comments_on_page)} li elements")
                if comments_on_page:
                    # Check the structure of the first li element
                    first_li = comments_on_page[0]
                    print(f"DEBUG: First li element classes: {first_li.get('class')}")
                    print(f"DEBUG: First li element HTML (first 300 chars): {str(first_li)[:300]}")

            if not comments_on_page:
                print(f"DEBUG: No comment elements found on page {page}. Stopping.")
                # Let's check what elements are actually in the HTML
                print(f"DEBUG: Available elements: {[tag.name for tag in soup.find_all()][:10]}")
                print(f"DEBUG: HTML sample: {html_content[:500]}")
                break

            print(f"DEBUG: Found {len(comments_on_page)} comments on page {page}.")
            for comment in comments_on_page:
                # Extract main comment data
                main_comment_div = comment.select_one('.comment-item')
                if not main_comment_div:
                    continue

                # Based on the HTML structure analysis:
                # Author is in .username
                author_elem = comment.select_one('.username')
                # Avatar is in .user-avatar img.avatar
                avatar_elem = comment.select_one('.user-avatar img.avatar')
                # Date is in .post-date
                date_elem = comment.select_one('.post-date')
                # Comment text is in .comment-text
                content_elem = comment.select_one('.comment-text')

                comment_data = {
                    'author': author_elem.text.strip() if author_elem else 'N/A',
                    'avatar_url': avatar_elem.get('src') if avatar_elem else None,
                    'timestamp': date_elem.text.strip() if date_elem else 'N/A',
                    'text': content_elem.text.strip() if content_elem else '',
                    'replies': []
                }

                # Extract replies (only direct replies, not nested ones)
                # Find the first .reply-comments container
                reply_container = comment.select_one('.reply-comments')
                if reply_container:
                    # Get only direct li children to avoid nested replies
                    reply_items = reply_container.select('ul > li')
                    for reply_item in reply_items:
                        # Skip "View More Replies" links
                        if reply_item.get('class') and 'show_replies' in reply_item.get('class'):
                            continue

                        reply_div = reply_item.select_one('.comment-item')
                        if not reply_div:
                            continue

                        reply_author = reply_item.select_one('.username')
                        reply_avatar = reply_item.select_one('.user-avatar img.avatar')
                        reply_date = reply_item.select_one('.post-date')
                        reply_text = reply_item.select_one('.comment-text')

                        reply_data = {
                            'author': reply_author.text.strip() if reply_author else 'N/A',
                            'avatar_url': reply_avatar.get('src') if reply_avatar else None,
                            'timestamp': reply_date.text.strip() if reply_date else 'N/A',
                            'text': reply_text.text.strip() if reply_text else ''
                        }

                        comment_data['replies'].append(reply_data)

                all_comments.append(comment_data)
            page += 1

        except (requests.exceptions.RequestException, json.JSONDecodeError) as e:
            print(f"DEBUG: An error occurred while fetching page {page}: {e}")
            break

    print(f"DEBUG: Total comments fetched: {len(all_comments)}")
    return all_comments

# --- API Endpoints ---

@app.route('/trending', methods=['GET'])
def get_trending():
    return jsonify(scrape_trending_novels())

@app.route('/search', methods=['GET'])
def search():
    query = request.args.get('q')
    if not query: return jsonify({'error': 'A search query `q` parameter is required.'}), 400
    return jsonify(search_novels(query))

@app.route('/novel', methods=['GET'])
def get_novel():
    novel_url = request.args.get('url')
    if not novel_url: return jsonify({'error': 'A `url` parameter is required.'}), 400
    if "novelfire.net" not in urlparse(novel_url).netloc:
        return jsonify({'error': 'Invalid URL provided.'}), 400
    data = scrape_novel_details(novel_url)
    return jsonify(data), 200 if 'error' not in data else 500

@app.route('/chapter', methods=['GET'])
def get_chapter():
    chapter_url = request.args.get('url')
    if not chapter_url: return jsonify({'error': 'A `url` parameter is required.'}), 400
    if "novelfire.net" not in urlparse(chapter_url).netloc:
        return jsonify({'error': 'Invalid URL provided.'}), 400
    data = scrape_chapter_content(chapter_url)
    return jsonify(data), 200 if 'error' not in data else 500

@app.route('/comments', methods=['GET'])
def get_comments():
    post_id = request.args.get('post_id')
    chapter_id = request.args.get('chapter_id')
    if not post_id or not chapter_id:
        return jsonify({'error': 'Both `post_id` and `chapter_id` are required.'}), 400
    return jsonify(fetch_comments(post_id, chapter_id))

@app.route('/debug', methods=['GET'])
def debug_info():
    """Debug endpoint to help troubleshoot deployment issues."""
    import platform
    import sys

    debug_data = {
        'environment': {
            'platform': platform.platform(),
            'python_version': sys.version,
            'is_vercel': 'VERCEL' in os.environ,
            'vercel_env': os.environ.get('VERCEL_ENV', 'unknown'),
            'user_agent_test': None
        },
        'test_request': None
    }

    # Test a simple request to see what happens
    try:
        scraper = cloudscraper.create_scraper()
        headers = {'User-Agent': 'Mozilla/5.0 (compatible; TestBot/1.0)'}
        response = scraper.get('https://httpbin.org/user-agent', headers=headers, timeout=10)
        debug_data['test_request'] = {
            'status': response.status_code,
            'user_agent_echo': response.json() if response.status_code == 200 else None
        }
    except Exception as e:
        debug_data['test_request'] = {'error': str(e)}

    return jsonify(debug_data)

@app.route('/test-access', methods=['GET'])
def test_novelfire_access():
    """Test different methods to access novelfire.net."""
    results = {}

    # Test 1: Basic requests
    try:
        response = requests.get('https://novelfire.net/home',
                              headers={'User-Agent': 'Mozilla/5.0'},
                              timeout=10)
        results['basic_requests'] = {
            'status': response.status_code,
            'success': response.status_code == 200
        }
    except Exception as e:
        results['basic_requests'] = {'error': str(e)}

    # Test 2: Cloudscraper
    try:
        scraper = cloudscraper.create_scraper()
        response = scraper.get('https://novelfire.net/home', timeout=10)
        results['cloudscraper'] = {
            'status': response.status_code,
            'success': response.status_code == 200
        }
    except Exception as e:
        results['cloudscraper'] = {'error': str(e)}

    # Test 3: Robust request function
    try:
        response = robust_request('https://novelfire.net/home')
        results['robust_request'] = {
            'status': response.status_code,
            'success': response.status_code == 200
        }
    except Exception as e:
        results['robust_request'] = {'error': str(e)}

    return jsonify(results)

# --- Main execution ---
if __name__ == '__main__':
    print("API is running on http://127.0.0.1:5000")
    # Set use_reloader=False to prevent the script from running twice, which is helpful for debugging.
    app.run(debug=True, use_reloader=False)